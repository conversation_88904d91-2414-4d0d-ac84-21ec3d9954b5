/*
 * This build file was generated by the Gradle 'init' task.
 *
 * This generated file contains a sample Java Library project to get you started.
 * For more details take a look at the Java Libraries chapter in the Gradle
 * user guide available at https://docs.gradle.org/3.5/userguide/java_library_plugin.html
 */

buildscript {
   repositories {
       mavenCentral()
   }
   configurations.maybeCreate("pitest")
   dependencies {
       classpath 'info.solidsoft.gradle.pitest:gradle-pitest-plugin:1.1.10'
       pitest 'com.github.alexvictoor:pitest-cucumber-plugin:0.4'
   }
}

plugins {
    id "info.solidsoft.pitest" version "1.1.11"
}


// Apply the java-library plugin to add support for Java Library
apply plugin: 'java-library'
apply plugin: "jacoco"

// In this section you declare where to find the dependencies of your project
repositories {
    // Use jcenter for resolving your dependencies.
    // You can declare any Maven/Ivy/file repository here.
    jcenter()
    mavenCentral()
}

jacoco {
    toolVersion = "0.7.6.************"
    // reportsDir = file("$buildDir/customJacocoReportDir")
}

jacocoTestReport {
    reports {
        xml.enabled true
        csv.enabled true
        html.enabled true
    }
}

test {
    testLogging.showStandardStreams = true
}

dependencies {
    // This dependency is exported to consumers, that is to say found on their compile classpath.
    api 'org.apache.commons:commons-math3:3.6.1'

    // This dependency is used internally, and not exposed to consumers on their own compile classpath.
    implementation 'com.google.guava:guava:21.0'

    // Use JUnit test framework
    testImplementation 'junit:junit:4.12'
    testCompile 'junit:junit:4.12'
    
    // cukes!
    testCompile 'info.cukes:cucumber-java:1.2.5' 
    testCompile 'info.cukes:cucumber-junit:1.2.5'
    
    // quickcheck!
    compile 'com.pholser:junit-quickcheck-core:0.7'
    compile 'com.pholser:junit-quickcheck-generators:0.7'
    testImplementation 'org.hamcrest:hamcrest-library:1.3'
}

 test {
     testLogging.showStandardStreams = true
 }

check.dependsOn jacocoTestReport
//check.dependsOn "pitest"

pitest {
    targetClasses = ['microwave.*']  //by default "${project.group}.*"
}
