import microwave.*;
import java.util.ArrayList;

public class TestRunner {
    public static void main(String[] args) {
        System.out.println("Testing basic microwave functionality...");
        
        // Create presets
        ArrayList<Preset> presets = new ArrayList<Preset>();
        presets.add(new Preset("Popcorn", 60, 10));
        presets.add(new Preset("Pizza 1 slice", 55, 6));
        presets.add(new Preset("Pizza 2 slice", 100, 6));
        presets.add(new Preset("Chicken breast", 40, 7));
        presets.add(new Preset("Defrost 1 lb hamburger", 180, 5));
        
        // Create microwave
        Microwave microwave = new Microwave(new ModeController(), new DisplayController(1), presets);
        
        // Test basic functionality
        System.out.println("Initial mode: " + microwave.getMode());
        
        // Test digit input
        microwave.setDoorOpen(false);
        microwave.tick();
        microwave.digitPressed(1);
        microwave.tick();
        microwave.digitPressed(2);
        microwave.tick();
        microwave.digitPressed(3);
        microwave.tick();
        microwave.digitPressed(4);
        microwave.tick();
        
        byte[] digits = microwave.digits();
        System.out.println("Digits after input: " + digits[0] + digits[1] + digits[2] + digits[3]);
        
        // Test preset
        microwave.presetPressed(1);
        microwave.tick();
        
        digits = microwave.digits();
        System.out.println("Digits after preset 1: " + digits[0] + digits[1] + digits[2] + digits[3]);
        System.out.println("Power level after preset 1: " + microwave.getPowerLevel());
        
        System.out.println("Basic functionality test completed successfully!");
    }
}
